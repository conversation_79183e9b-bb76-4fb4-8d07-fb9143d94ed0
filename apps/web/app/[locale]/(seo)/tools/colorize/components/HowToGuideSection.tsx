import React from 'react'
import { UploadCloud, Settings, Download } from 'lucide-react'
import StepsCarouselClient from './StepsCarouselClient'
import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'

export interface Step {
  icon: React.ReactNode
  title: string
  description: string
  image: string
}

export default async function StepsCarousel({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('colorize')

  const steps: Step[] = [
    {
      icon: <UploadCloud className="w-10 h-10 text-[#339bfa]" />,
      title: t('step1Title'),
      description: t('step1Description'),
      image: 'https://picsum.photos/id/15/200/300',
    },
    {
      icon: <Settings className="w-10 h-10 text-pink-400" />,
      title: t('step2Title'),
      description: t('step2Description'),
      image: 'https://picsum.photos/id/16/200/300',
    },
    {
      icon: <Download className="w-10 h-10 text-fuchsia-500" />,
      title: t('step3Title'),
      description: t('step3Description'),
      image: 'https://picsum.photos/id/17/200/300',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('howToTitle')}
        </h2>

        <StepsCarouselClient steps={steps} />

        <div className="mt-24 text-center">
          <div className="relative group inline-block w-full md:w-auto">
            <Link href={toolUrl}>
              <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 w-full md:w-auto inline-block px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
                {t('howToButton')}
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
